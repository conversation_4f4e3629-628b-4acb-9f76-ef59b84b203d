//background.js

console.log('Background script loaded.');

// Cached configuration for API calls
let cachedConfig = null;

// Content buffering for streaming responses
let contentBuffer = '';

// Request queue to prevent concurrent API calls
let requestQueue = [];
let isProcessingRequest = false;

// Cache for API responses
const responseCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Force cache clear on startup to ensure fresh config
cachedConfig = null;

// Utility function to safely post messages to ports
function safePostMessage(port, message) {
  try {
    if (port && !port.error) {
      port.postMessage(message);
    }
  } catch (error) {
    if (error.message && error.message.includes('Extension context invalidated')) {
      console.log('Extension context invalidated, cannot send message');
    } else {
      console.error('Error sending message:', error);
    }
  }
}

// Create context menu items when the extension is installed or updated
chrome.runtime.onInstalled.addListener(() => {
  console.log('Extension installed.');

  // Remove any existing context menus to prevent duplicates
  chrome.contextMenus.removeAll(() => {
    // Context menu for bulleting the entire page
    chrome.contextMenus.create({
      id: "bulletPage",
      title: "Bullet page",
      contexts: ["all"]
    });

    // Context menu for bulleting selected text
    chrome.contextMenus.create({
      id: "bulletSelection",
      title: "Bullet selection",
      contexts: ["selection"]
    });
  });
});

// Handle context menu item clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  console.log('Context menu item clicked:', info.menuItemId);
  if (info.menuItemId === "bulletPage" || info.menuItemId === "bulletSelection") {
    // First, inject the content script into the tab
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      files: ['content.js']
    }, () => {
      // Now send the message to the content script
      console.log('Sending message to content script.');
      chrome.tabs.sendMessage(tab.id, {
        action: info.menuItemId === "bulletPage" ? 'summarizePage' : 'summarizeSelection'
      });
    });
  }
});

// Listen for connections from content script
chrome.runtime.onConnect.addListener(function(port) {
  console.log('Connected to port:', port.name);
  if (port.name === 'openai') {
    port.onMessage.addListener(function(msg) {
      try {
        if (msg.action === 'callOpenAI' && msg.text) {
          callOpenAIStream(msg.text, port);
        } else if (msg.action === 'generateTitle' && msg.summary) {
          generateTitle(msg.summary, port);
        } else if (msg.action === 'callOpenAIContext' && msg.text) {
          callOpenAIContextStream(msg.text, port);
        }
      } catch (error) {
        if (error.message.includes('Extension context invalidated')) {
          console.log('Extension context invalidated, ignoring message');
          return;
        }
        console.error('Error handling message:', error);
      }
    });

    port.onDisconnect.addListener(function() {
      if (chrome.runtime.lastError) {
        console.log('Port disconnected:', chrome.runtime.lastError.message);
      }
    });
  }
});

/**
 * Retrieve configuration settings (with caching).
 */
async function getConfig() {
  if (cachedConfig) {
    return cachedConfig;
  }
  cachedConfig = new Promise((resolve) => {
    chrome.storage.local.get(['customApiKey', 'bulletsSettings'], (data) => {
      const settings = data.bulletsSettings || {};
      resolve({
        API_ENDPOINT: 'https://generativelanguage.googleapis.com/v1beta/chat/completions',
        MODEL: settings.model || 'gemini-2.5-flash-lite',
        TIMEOUT_MS: 30000,
        TEMPERATURE: 0.7,
        API_KEY: data.customApiKey || 'api-key',
        LANGUAGE: settings.language || 'french',
        CUSTOM_LANGUAGE: settings.customLanguage || '',
        PROMPTS: settings.prompts || getDefaultPrompts()
      });
    });
  });
  return cachedConfig;
}

/**
 * Process queued requests to prevent concurrent API calls
 */
async function processRequestQueue() {
  if (isProcessingRequest || requestQueue.length === 0) {
    return;
  }

  isProcessingRequest = true;
  const { request, resolve, reject } = requestQueue.shift();

  try {
    const result = await request();
    resolve(result);
  } catch (error) {
    reject(error);
  } finally {
    isProcessingRequest = false;
    // Process next request in queue
    setTimeout(processRequestQueue, 100); // Small delay to prevent overwhelming API
  }
}

/**
 * Queue an API request to prevent concurrent calls
 */
function queueApiRequest(request) {
  return new Promise((resolve, reject) => {
    requestQueue.push({ request, resolve, reject });
    processRequestQueue();
  });
}

/**
 * Get cached response if available and not expired
 */
function getCachedResponse(key) {
  const cached = responseCache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  responseCache.delete(key);
  return null;
}

/**
 * Cache API response
 */
function setCachedResponse(key, data) {
  responseCache.set(key, {
    data,
    timestamp: Date.now()
  });
}

/**
 * Consolidated error handler for API fetch responses.
 */
async function handleErrorResponse(response, port) {
  try {
    const text = await response.text();
    console.error('Error response body:', text);
    let errorMsg = text;
    try {
      const errorData = JSON.parse(text);
      errorMsg = errorData.error?.message || text;
      console.error('Parsed error data:', errorData);
    } catch (e) {
      console.error('Error parsing error response:', e);
    }
    safePostMessage(port, { type: 'error', error: errorMsg });
  } catch (e) {
    console.error('Error handling error response:', e);
    safePostMessage(port, { type: 'error', error: 'Unknown error occurred' });
  }
}

/**
 * Call OpenAI API with streaming using async/await.
 * Enhanced with queuing and caching for better performance.
 */
async function callOpenAIStream(text, port) {
  const CONFIG = await getConfig();

  console.log('Starting API call with config:', {
    endpoint: CONFIG.API_ENDPOINT,
    model: CONFIG.MODEL,
    textLength: text.length
  });

  if (!text || !port) {
    console.error('Missing required parameters');
    port?.postMessage({ type: 'error', error: 'Invalid parameters for API call' });
    return;
  }

  // Check cache for similar requests
  const cacheKey = `${CONFIG.MODEL}_${text.substring(0, 100)}_${CONFIG.LANGUAGE}`;
  const cachedResponse = getCachedResponse(cacheKey);
  if (cachedResponse) {
    console.log('Using cached response');
    safePostMessage(port, { type: 'data', content: cachedResponse });
    safePostMessage(port, { type: 'done' });
    return;
  }

  // Use the prompt template for summarization
  const prompt = await PROMPT_TEMPLATES.summarize(text);

  const body = JSON.stringify({
    model: CONFIG.MODEL,
    messages: [{ role: 'user', content: prompt }],
    temperature: CONFIG.TEMPERATURE,
    stream: true
  });
  console.log('Request body:', body);

  // Queue the API request to prevent concurrent calls
  await queueApiRequest(async () => {
    // Set up a timeout for the fetch
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), CONFIG.TIMEOUT_MS);

    try {
      const response = await fetch(CONFIG.API_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${CONFIG.API_KEY}`
        },
        body,
        signal: controller.signal
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        await handleErrorResponse(response, port);
        return;
      }

      // Process the streamed response using a while loop
      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let fullContent = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          safePostMessage(port, { type: 'done' });
          // Cache the full response for future requests
          setCachedResponse(cacheKey, fullContent);
          break;
        }
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n').filter(line => line.trim() !== '');
        for (const line of lines) {
          const message = line.replace(/^data: /, '');
          if (message === '[DONE]') {
            safePostMessage(port, { type: 'done' });
            setCachedResponse(cacheKey, fullContent);
            return;
          }
          try {
            const parsed = JSON.parse(message);
            const content = parsed.choices[0].delta.content;
            if (content) {
              fullContent += content;
              // Buffer and clean content before sending
              const cleanedContent = bufferAndCleanContent(content);
              if (cleanedContent) {
                safePostMessage(port, { type: 'data', content: cleanedContent });
              }
            }
          } catch (error) {
            console.error('Error parsing JSON:', error);
          }
        }
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        console.error('Request timed out');
        safePostMessage(port, { type: 'error', error: 'Request timed out after 30 seconds' });
      } else if (error.message && error.message.includes('Extension context invalidated')) {
        console.log('Extension context invalidated during API call, ignoring');
        return;
      } else {
        console.error('Error calling Gemini API:', error);
        safePostMessage(port, { type: 'error', error: error.toString() });
      }
    } finally {
      clearTimeout(timeout);
    }
  });
}

/**
 * Generate title from summary using async/await.
 */
async function generateTitle(summary, port) {
  const CONFIG = await getConfig();
  console.log('Starting title generation...');

  if (!summary || !port) {
    console.error('Missing required parameters for title generation');
    port?.postMessage({ type: 'error', error: 'Invalid parameters for title generation' });
    return;
  }

  const prompt = await PROMPT_TEMPLATES.generateTitle(summary);

  // Set up a timeout for the fetch
  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), CONFIG.TIMEOUT_MS);

  try {
    const response = await fetch(CONFIG.API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CONFIG.API_KEY}`
      },
      body: JSON.stringify({
        model: CONFIG.MODEL,
        messages: [{ role: 'user', content: prompt }],
        temperature: CONFIG.TEMPERATURE,
        stream: false
      }),
      signal: controller.signal
    });

    console.log('Title generation response status:', response.status);

    if (!response.ok) {
      await handleErrorResponse(response, port);
      return;
    }

    const data = await response.json();
    console.log('Title generation API response:', data);

    // Extract title using the expected response format
    const title = data.choices?.[0]?.message?.content;
    if (!title) {
      throw new Error('No title found in response');
    }

    // Clean and process the title
    const cleanTitle = title
      .replace(/['"]/g, '')
      .replace(/\n/g, ' ')
      .replace(/^Title: /i, '')
      .trim();

    console.log('Final processed title:', cleanTitle);
    safePostMessage(port, { type: 'title', title: cleanTitle });
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error('Title generation request timed out');
      safePostMessage(port, { type: 'error', error: 'Title generation request timed out after 30 seconds' });
    } else if (error.message && error.message.includes('Extension context invalidated')) {
      console.log('Extension context invalidated during title generation, ignoring');
      return;
    } else {
      console.error('Error in title generation:', error);
      safePostMessage(port, { type: 'error', error: error.toString() });
    }
  } finally {
    clearTimeout(timeout);
  }
}

/**
 * Call the API for providing context (with streaming) using async/await.
 */
async function callOpenAIContextStream(text, port) {
  const CONFIG = await getConfig();

  console.log('Starting context API call with config:', {
    endpoint: CONFIG.API_ENDPOINT,
    model: CONFIG.MODEL,
    textLength: text.length
  });

  if (!text || !port) {
    console.error('Missing required parameters for context API call');
    port?.postMessage({ type: 'error', error: 'Invalid parameters for context API call' });
    return;
  }

  // Use the prompt template for providing context information
  const prompt = await PROMPT_TEMPLATES.provideContext(text);

  // Set up an abort controller with a timeout
  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), CONFIG.TIMEOUT_MS);

  const body = JSON.stringify({
    model: CONFIG.MODEL,
    messages: [{ role: 'user', content: prompt }],
    temperature: CONFIG.TEMPERATURE,
    stream: true
  });
  console.log('Context request body:', body);

  // Check cache for similar requests
  const cacheKey = `${CONFIG.MODEL}_${text.substring(0, 100)}_${CONFIG.LANGUAGE}`;
  const cachedResponse = getCachedResponse(cacheKey);
  if (cachedResponse) {
    console.log('Using cached context response');
    safePostMessage(port, { type: 'data', content: cachedResponse });
    safePostMessage(port, { type: 'done' });
    return;
  }

  try {
    const response = await fetch(CONFIG.API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CONFIG.API_KEY}`
      },
      body,
      signal: controller.signal
    });
    console.log('Context response status:', response.status);

    if (!response.ok) {
      await handleErrorResponse(response, port);
      return;
    } else {
      // Process the streamed response using a while loop
      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let fullContent = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          safePostMessage(port, { type: 'done' });
          // Cache the full response for future requests
          setCachedResponse(cacheKey, fullContent);
          break;
        }
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n').filter(line => line.trim() !== '');
        for (const line of lines) {
          const message = line.replace(/^data: /, '');
          if (message === '[DONE]') {
            safePostMessage(port, { type: 'done' });
            setCachedResponse(cacheKey, fullContent);
            return;
          }
          try {
            const parsed = JSON.parse(message);
            const content = parsed.choices[0].delta.content;
            if (content) {
              fullContent += content;
              // Buffer and clean content before sending
              const cleanedContent = bufferAndCleanContent(content);
              if (cleanedContent) {
                safePostMessage(port, { type: 'data', content: cleanedContent });
              }
            }
          } catch (error) {
            console.error('Error parsing JSON:', error);
          }
        }
      }
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error('Request timed out');
      safePostMessage(port, { type: 'error', error: 'Request timed out after 30 seconds' });
    } else if (error.message && error.message.includes('Extension context invalidated')) {
      console.log('Extension context invalidated during API call, ignoring');
      return;
    } else {
      console.error('Error calling Gemini API:', error);
      safePostMessage(port, { type: 'error', error: error.toString() });
    }
  } finally {
    clearTimeout(timeout);
  }
    });
  } catch (error) {
    console.error('Error in queued request:', error);
    safePostMessage(port, { type: 'error', error: error.toString() });
  }
}
}
}
      // Process the streamed response using a while loop
      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          safePostMessage(port, { type: 'done' });
          break;
        }
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n').filter(line => line.trim() !== '');
        for (const line of lines) {
          const message = line.replace(/^data: /, '');
          if (message === '[DONE]') {
            safePostMessage(port, { type: 'done' });
            return;
          }
          try {
            const parsed = JSON.parse(message);
            const content = parsed.choices[0].delta.content;
            if (content) {
              const cleanedContent = bufferAndCleanContent(content);
              if (cleanedContent) {
                safePostMessage(port, { type: 'data', content: cleanedContent });
              }
            }
          } catch (error) {
            console.error('Error parsing JSON (context):', error);
          }
        }
      }
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error('Context request timed out');
      safePostMessage(port, { type: 'error', error: 'Request timed out after 30 seconds' });
    } else if (error.message && error.message.includes('Extension context invalidated')) {
      console.log('Extension context invalidated during context API call, ignoring');
      return;
    } else {
      console.error('Error calling context API:', error);
      safePostMessage(port, { type: 'error', error: error.toString() });
    }
  } finally {
    clearTimeout(timeout);
  }
}

/**
 * Get default prompt templates
 */
function getDefaultPrompts() {
  return {
    summarize: `You are an expert reporter tasked with creating clear, engaging, and informative outlines of various texts. Your goal is to capture the most important details while presenting them in a structured and easily digestible format.

Please follow these steps to create your summary:

1. Carefully read through the entire text and:
   a. Identify the key takeaways in the text.
   b. For each takeaway, identify the most important and interesting points.
   c. Present each point clearly and concisely, using analogies or relatable examples.
   d. Ensure you're capturing the most salient details from the original text.
   e. Check that each point can be expressed concisely in 1-2 lines (15-30 words).

Based on your analysis, create a structured outline summary following these rules:
   - The first section title is "Title: Key Takeaways".
   - The following sections go into more detail on each takeaway.
   - Start each section with "Title: " followed by title of the takeaway.
   - Use bullet points (•) for each key point under a title.
   - Keep points concise (1-2 lines) and engaging.
   - End each point with a period.
   - Do not use sub-bullets or nested points.
   - Do not use any extra formatting or special characters.

Your final output should follow this exact format:

Title: [Key takeaways]
• takeaway 1
• takeaway 2
...

Title: next takeaways
• [Point 1]
• [Point 2]

...

Remember to make your summary engaging and informative, capturing the most important details from the original text while tailoring it to the target audience. It is essential for you not to repeat yourself.
Here is the text you need to summarize:
{TEXT}`,

    context: `Provide background information about the topic, that's not in the article. If the event has a long timeline, explain the last significant events leading up to it. Be concise and to the point. Use bullet points, but no ** or sub-bullets.
Your final output should follow this exact format:

   - Start each section with "Title: " followed by title of the section.
   - Use bullet points (•) for each key point under a title.
   - Keep points concise (3-5 lines) and engaging.
   - End each point with a period.
   - Do not use sub-bullets or nested points.
   - Do not use any extra formatting or special characters.

Here is an example of the format you should follow:

Title: [Background information]
• background 1
• background 2
...
Title: [Approximate Timeline]
• Event 1
• Event 2
...

Here is the text you need to provide background information for:
{TEXT}`,

    title: `Create a short title (3-7 words) that captures the main topic.
Only output the title itself, with no prefix or formatting.

Text to summarize:
{TEXT}`
  };
}

/**
 * Get language instruction based on settings
 */
function getLanguageInstruction(config) {
  switch (config.LANGUAGE) {
    case 'french':
      return 'IMPORTANT: You must reply ONLY in French. All your response must be in French language.';
    case 'english':
      return 'IMPORTANT: You must reply ONLY in English. All your response must be in English language.';
    case 'custom':
      return config.CUSTOM_LANGUAGE ? `IMPORTANT: You must reply ONLY in ${config.CUSTOM_LANGUAGE}. All your response must be in ${config.CUSTOM_LANGUAGE} language.` : '';
    default:
      return 'IMPORTANT: You must reply ONLY in French. All your response must be in French language.';
  }
}

/**
 * Buffer and clean content before sending to content script
 * Enhanced with intelligent chunking and performance optimization
 */
function bufferAndCleanContent(content) {
  if (!contentBuffer) {
    contentBuffer = '';
  }

  contentBuffer += content;

  // Only send complete words/phrases to avoid UI flickering
  const lastWordBoundary = findLastWordBoundary(contentBuffer);

  // Enhanced logic: send content when buffer reaches minimum size or complete boundary
  const MIN_CHUNK_SIZE = 5; // Minimum characters to send
  const MAX_CHUNK_SIZE = 100; // Maximum characters to buffer before forcing send

  if (lastWordBoundary > 0 && (lastWordBoundary >= MIN_CHUNK_SIZE || contentBuffer.length >= MAX_CHUNK_SIZE)) {
    const contentToSend = contentBuffer.substring(0, lastWordBoundary);
    contentBuffer = contentBuffer.substring(lastWordBoundary);

    // Clean the content to remove artifacts
    const cleaned = contentToSend
      .replace(/\s+/g, ' ')
      .replace(/\n+/g, '\n')
      .trim();

    return cleaned || null;
  }

  return null;
}

/**
 * Find the last word boundary in the buffer
 */
function findLastWordBoundary(text) {
  const wordBoundaries = [
    /\s+/g,          // whitespace
    /[.,!?;:]/g,     // punctuation
    /\n/g           // newlines
  ];

  let lastBoundary = -1;
  for (const regex of wordBoundaries) {
    let match;
    while ((match = regex.exec(text)) !== null) {
      lastBoundary = match.index + match[0].length;
    }
  }

  return lastBoundary;
}

// Prompt templates used for summarization, title generation, and providing context
const PROMPT_TEMPLATES = {
  summarize: async (text) => {
    const CONFIG = await getConfig();
    const languageInstruction = getLanguageInstruction(CONFIG);
    const template = CONFIG.PROMPTS.summarize.replace('{TEXT}', text);
    // Add language instruction at the beginning of the prompt for better effectiveness
    return languageInstruction ? `${languageInstruction}\n\n${template}` : template;
  },

  generateTitle: async (summary) => {
    const CONFIG = await getConfig();
    const languageInstruction = getLanguageInstruction(CONFIG);
    const template = CONFIG.PROMPTS.title.replace('{TEXT}', summary.slice(0, 500));
    // Add language instruction at the beginning of the prompt for better effectiveness
    return languageInstruction ? `${languageInstruction}\n\n${template}` : template;
  },

  provideContext: async (text) => {
    const CONFIG = await getConfig();
    const languageInstruction = getLanguageInstruction(CONFIG);
    const template = CONFIG.PROMPTS.context.replace('{TEXT}', text);
    // Add language instruction at the beginning of the prompt for better effectiveness
    return languageInstruction ? `${languageInstruction}\n\n${template}` : template;
  }
};

const FORMAT_MARKERS = /^(Title:|•|-|\*)/;

/**
 * Buffer and clean content received via streaming.
 * Preserves formatting markers and processes complete lines.
 */
function bufferAndCleanContent(content) {
  contentBuffer += content;

  // Look for complete lines and formatting markers
  const lines = contentBuffer.split('\n');

  // Keep the last line in the buffer if it's incomplete
  const lastLine = lines[lines.length - 1];
  if (!lastLine.match(FORMAT_MARKERS) && !lastLine.trim().endsWith('.')) {
    contentBuffer = lines.pop() || '';
  } else {
    contentBuffer = '';
  }

  // Process complete lines
  if (lines.length === 0) return '';

  return lines
    .map(line => {
      // Preserve formatting markers
      const formatMatch = line.match(FORMAT_MARKERS);
      const prefix = formatMatch ? formatMatch[0] : '';
      const rest = line.slice(prefix.length);

      // Clean the content while preserving the prefix
      const cleanedRest = cleanContent(rest);
      return prefix + cleanedRest;
    })
    .join('\n');
}

/**
 * Normalize content by reducing multiple spaces to a single space.
 */
function cleanContent(content) {
  return content.replace(/\s+/g, ' ');
}

// Optimize the Save Note logic using async/await for asynchronous operations
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.action === 'clearConfigCache') {
    // Clear the cached configuration
    cachedConfig = null;
    sendResponse({ success: true });
    return true;
  }

  if (request.action === 'saveNote') {
    console.log('Received save note request:', request.note);

    (async () => {
      try {
        if (!request.note || !request.note.title || !request.note.content) {
          throw new Error('Invalid note data');
        }
        const savedNotes = await new Promise((resolve, reject) => {
          chrome.storage.local.get('savedNotes', (data) => {
            if (chrome.runtime.lastError) {
              reject(chrome.runtime.lastError);
            } else {
              resolve(data.savedNotes || []);
            }
          });
        });

        savedNotes.push(request.note);

        await new Promise((resolve, reject) => {
          chrome.storage.local.set({ savedNotes }, () => {
            if (chrome.runtime.lastError) {
              reject(chrome.runtime.lastError);
            } else {
              resolve();
            }
          });
        });

        console.log('Note saved successfully');
        sendResponse({ success: true });
      } catch (error) {
        console.error('Error saving note:', error);
        sendResponse({ success: false, error: error.message });
      }
    })();

    return true; // Keep the message channel open for async response
  }
});
