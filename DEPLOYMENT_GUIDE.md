# Bullets Chrome Extension - Deployment Guide

## Overview

This guide covers the complete deployment process for the Bullets Chrome Extension, including development setup, testing, packaging, and publishing to the Chrome Web Store.

## Table of Contents

- [Development Environment Setup](#development-environment-setup)
- [Testing Procedures](#testing-procedures)
- [Build Process](#build-process)
- [Chrome Web Store Deployment](#chrome-web-store-deployment)
- [Version Management](#version-management)
- [Continuous Integration](#continuous-integration)
- [Monitoring & Maintenance](#monitoring--maintenance)

## Development Environment Setup

### Prerequisites

#### Required Software
- **Node.js**: Version 16 or higher
- **npm**: Latest stable version
- **Chrome Browser**: Version 88 or higher
- **Git**: For version control

#### Required Accounts
- **Google AI Studio**: For API key testing
- **Chrome Web Store Developer Account**: For publishing
- **GitHub Account**: For repository hosting

### Local Development Setup

#### 1. Clone Repository
```bash
git clone https://github.com/your-username/bullets-extension.git
cd bullets-extension
```

#### 2. Install Dependencies
```bash
npm install
```

#### 3. Development Tools Setup
```bash
# Install development dependencies
npm install --save-dev eslint stylelint html-validate
npm install --save-dev postcss postcss-preset-env
npm install --save-dev imagemin-cli
```

#### 4. Load Extension in Chrome

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top-right corner)
3. Click "Load unpacked"
4. Select the extension root directory
5. Verify the extension appears in the extensions list

#### 5. Configure Development Settings

```javascript
// Enable debug mode in background.js
const DEBUG_MODE = true;
console.log('Bullets Extension - Debug Mode Enabled');
```

### Environment Configuration

#### Development Configuration
```json
{
  "name": "Bullets - Summarize the web (Dev)",
  "version": "2.0.0-dev",
  "description": "Development version for testing"
}
```

#### Production Configuration
```json
{
  "name": "Bullets - Summarize the web",
  "version": "2.0.0",
  "description": "Powerful web content summarization with AI"
}
```

## Testing Procedures

### Automated Testing

#### 1. Unit Tests
```bash
# Run unit tests
npm test

# Run tests with coverage
npm run test:coverage
```

#### 2. Linting
```bash
# Lint JavaScript files
npm run lint:js

# Lint CSS files
npm run lint:css

# Lint HTML files
npm run lint:html
```

### Manual Testing Checklist

#### Core Functionality Tests

**✅ Extension Installation**
- [ ] Extension loads without errors
- [ ] Context menus appear on right-click
- [ ] Popup interface opens correctly
- [ ] Settings page loads properly

**✅ Summarization Features**
- [ ] "Bullet page" works on various websites
- [ ] "Bullet selection" works with selected text
- [ ] Real-time streaming display functions
- [ ] Copy button copies summary correctly
- [ ] Save button saves notes to storage

**✅ UI/UX Tests**
- [ ] Drag and drop functionality works
- [ ] Resize handles work correctly
- [ ] Dark/light mode switching works
- [ ] Responsive behavior on different screen sizes
- [ ] Keyboard navigation functions

#### API Integration Tests

**✅ Google Gemini AI Integration**
- [ ] Valid API key works correctly
- [ ] Invalid API key shows appropriate error
- [ ] Rate limiting handled gracefully
- [ ] Network errors recovered properly
- [ ] All three models (Flash Lite, Flash, Pro) work

**✅ Error Handling Tests**
- [ ] Timeout errors handled (30-second limit)
- [ ] Extension context invalidation handled
- [ ] Large content processing works
- [ ] Malformed responses handled

### Performance Testing

#### Load Testing
```javascript
// Test with large content
const largeContent = 'Very long article content...'.repeat(1000);
await testSummarization(largeContent);
```

#### Memory Testing
```javascript
// Monitor memory usage
console.log('Memory usage:', performance.memory);
```

### Browser Compatibility Testing

#### Supported Browsers
- **Chrome**: 88+ ✅
- **Edge**: 88+ ✅
- **Opera**: 74+ ✅
- **Brave**: 88+ ✅

#### Test Matrix
| Browser | Version | Status | Notes |
|---------|---------|--------|-------|
| Chrome | 88+ | ✅ | Full support |
| Edge | 88+ | ✅ | Full support |
| Opera | 74+ | ✅ | Full support |
| Firefox | 109+ | ⚠️ | Limited support |

## Build Process

### Development Build

#### 1. Source Code Preparation
```bash
# Format code
npm run format

# Run linting checks
npm run lint
```

#### 2. Asset Optimization
```bash
# Minify JavaScript
npx terser background.js -o dist/background.js
npx terser content.js -o dist/content.js
npx terser popup.js -o dist/popup.js
npx terser settings.js -o dist/settings.js

# Optimize CSS
npx postcss content.css -o dist/content.css

# Compress icons
npx imagemin icons/* --out-dir=dist/icons/
```

#### 3. Package Creation
```bash
# Create development package
zip -r bullets-dev-v2.0.0.zip dist/*
```

### Production Build

#### 1. Production Configuration
```bash
# Update manifest.json for production
node scripts/update-manifest.js --env=production
```

#### 2. Code Optimization
```bash
# Advanced minification
npx terser background.js -o dist/background.js \
  --compress sequences=true,dead_code=true,conditionals=true,booleans=true,unused=true \
  --mangle=true

# CSS optimization
npx postcss content.css -o dist/content.css \
  --use postcss-preset-env --env=production
```

#### 3. Package Validation
```bash
# Validate package structure
npx chrome-webstore-upload validate \
  --source=bullets-v2.0.0.zip \
  --auto-publish=false
```

### Build Scripts

#### package.json Build Commands
```json
{
  "scripts": {
    "build": "npm run clean && npm run lint && npm run test && npm run optimize",
    "build:dev": "npm run clean && npm run optimize:dev",
    "build:prod": "npm run clean && npm run lint && npm run test && npm run optimize:prod",
    "optimize": "npm run optimize:js && npm run optimize:css && npm run optimize:images",
    "optimize:dev": "mkdir -p dist && cp -r * dist/ 2>/dev/null || true",
    "optimize:prod": "node scripts/build.js",
    "clean": "rm -rf dist/*",
    "lint": "npm run lint:js && npm run lint:css && npm run lint:html",
    "lint:js": "eslint *.js",
    "lint:css": "stylelint *.css",
    "lint:html": "html-validate *.html",
    "test": "node tests/run-tests.js",
    "package": "node scripts/package.js"
  }
}
```

## Chrome Web Store Deployment

### Account Setup

#### 1. Create Developer Account
1. Go to [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole)
2. Sign in with Google account
3. Pay one-time $5 developer registration fee
4. Accept developer agreement

#### 2. Developer Dashboard Setup
- Configure payment profile
- Set up tax information
- Add support contact information
- Configure privacy policy URL

### Extension Submission

#### 1. Prepare Store Listing

**Basic Information:**
- Extension name: "Bullets - Summarize the web"
- Description: Clear, compelling description
- Category: "Productivity"
- Language: English (primary)

**Visual Assets:**
- Icon (128x128 PNG)
- Screenshots (1280x800, 640x400)
- Promo tile (440x280)
- Marquee tile (1400x560)

**Technical Details:**
- Website URL: GitHub repository
- Support URL: GitHub issues
- Privacy policy URL: Required for data collection

#### 2. Upload Package

```bash
# Upload using Chrome Web Store Upload CLI
npx chrome-webstore-upload upload \
  --source=bullets-v2.0.0.zip \
  --extension-id=your-extension-id \
  --client-id=your-client-id \
  --client-secret=your-client-secret \
  --refresh-token=your-refresh-token
```

#### 3. Configure Permissions

**Required Permissions:**
- `contextMenus`: For right-click functionality
- `activeTab`: For accessing current tab content
- `scripting`: For content script injection
- `storage`: For saving notes and settings
- `commands`: For keyboard shortcuts

**Host Permissions:**
- `<all_urls>`: For content summarization on any website
- `https://generativelanguage.googleapis.com/*`: For Gemini AI API

#### 4. Privacy & Security

**Privacy Practices:**
- Data collection: API usage analytics (optional)
- Data usage: Only for extension functionality
- Data sharing: No third-party sharing

**Security Measures:**
- Content Security Policy implemented
- No external script loading
- Secure API key storage
- Input validation and sanitization

### Review Process

#### Initial Review Timeline
- **New Extensions**: 1-2 business days
- **Updates**: 1-2 business days
- **Appeals**: Additional 2-3 business days

#### Common Rejection Reasons
- ❌ Missing privacy policy
- ❌ Unclear purpose or functionality
- ❌ Excessive permissions
- ❌ Non-functional features
- ❌ Poor user experience
- ❌ Security vulnerabilities

#### Appeal Process
1. Review rejection feedback carefully
2. Fix identified issues
3. Submit detailed explanation
4. Provide additional documentation if needed

### Post-Launch Management

#### 1. Monitor Performance
```javascript
// Extension analytics (if implemented)
chrome.runtime.onMessage.addListener((message) => {
  if (message.type === 'analytics') {
    // Send to analytics service
  }
});
```

#### 2. Handle User Reviews
- Respond to reviews within 24-48 hours
- Address bug reports promptly
- Thank users for positive feedback
- Request additional information when needed

#### 3. Update Management
- Monitor Chrome Web Store notifications
- Plan regular updates (monthly recommended)
- Communicate changes through release notes

## Version Management

### Version Numbering

**Semantic Versioning:** `MAJOR.MINOR.PATCH`

- **MAJOR**: Breaking changes, major feature additions
- **MINOR**: New features, backwards compatible
- **PATCH**: Bug fixes, minor improvements

**Examples:**
- `2.0.0`: Major release with new features
- `2.1.0`: New feature addition
- `2.1.1`: Bug fix release

### Changelog Management

#### CHANGELOG.md Format
```markdown
# Changelog

## [2.0.0] - 2024-01-15
### Added
- New Gemini AI integration
- Real-time streaming responses
- Advanced settings page

### Changed
- Improved UI with glassmorphism design
- Enhanced error handling
- Better performance optimization

### Fixed
- Memory leak in streaming responses
- Context menu not appearing on some sites
- Settings not persisting correctly

## [1.5.0] - 2023-12-01
### Added
- Dark mode support
- Export/import settings functionality
```

### Release Process

#### 1. Pre-Release Checklist
- [ ] All tests passing
- [ ] Code linted and formatted
- [ ] Documentation updated
- [ ] Version number updated
- [ ] Changelog updated
- [ ] Build process successful

#### 2. Release Steps
```bash
# 1. Create release branch
git checkout -b release/v2.0.0

# 2. Update version numbers
node scripts/update-version.js 2.0.0

# 3. Build production package
npm run build:prod

# 4. Create git tag
git tag -a v2.0.0 -m "Release version 2.0.0"

# 5. Push changes
git push origin release/v2.0.0
git push origin v2.0.0

# 6. Create GitHub release
gh release create v2.0.0 \
  --title "Bullets v2.0.0" \
  --notes-file CHANGELOG.md \
  --latest
```

#### 3. Post-Release
- Monitor error reports
- Check user feedback
- Prepare for next development cycle

## Continuous Integration

### GitHub Actions Setup

#### .github/workflows/ci.yml
```yaml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
    - name: Install dependencies
      run: npm ci
    - name: Run linting
      run: npm run lint
    - name: Run tests
      run: npm test
    - name: Build extension
      run: npm run build

  deploy:
    needs: test
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
    - name: Deploy to Chrome Web Store
      uses: chrome-webstore-upload-action@v2
      with:
        extension-id: ${{ secrets.EXTENSION_ID }}
        client-id: ${{ secrets.CLIENT_ID }}
        client-secret: ${{ secrets.CLIENT_SECRET }}
        refresh-token: ${{ secrets.REFRESH_TOKEN }}
        source: dist/bullets.zip
```

### Automated Testing

#### Unit Test Setup
```javascript
// tests/background.test.js
const { getConfig } = require('../background.js');

test('getConfig returns valid configuration', async () => {
  const config = await getConfig();
  expect(config).toHaveProperty('API_ENDPOINT');
  expect(config).toHaveProperty('MODEL');
});
```

#### Integration Test Setup
```javascript
// tests/integration.test.js
describe('Extension Integration', () => {
  test('summarization workflow', async () => {
    // Test complete workflow
  });
});
```

### Code Quality Gates

#### Pre-commit Hooks
```bash
# Install husky
npm install --save-dev husky

# Setup hooks
npx husky install
npx husky add .husky/pre-commit "npm run lint"
npx husky add .husky/pre-push "npm test"
```

## Monitoring & Maintenance

### Performance Monitoring

#### Key Metrics to Track

**Technical Metrics:**
- Extension load time
- API response times
- Memory usage
- Error rates
- User engagement

**Business Metrics:**
- Active users
- Session duration
- Feature usage
- User retention

#### Monitoring Tools

```javascript
// Performance monitoring
const performanceMonitor = {
  startTime: Date.now(),
  apiCalls: 0,
  errors: 0,

  logPerformance() {
    console.log('Performance metrics:', {
      uptime: Date.now() - this.startTime,
      apiCalls: this.apiCalls,
      errorRate: this.errors / this.apiCalls
    });
  }
};
```

### Error Reporting

#### Error Tracking Setup
```javascript
// Global error handler
window.addEventListener('error', (event) => {
  // Report to error tracking service
  reportError({
    message: event.error.message,
    stack: event.error.stack,
    url: event.filename,
    line: event.lineno
  });
});

// Chrome runtime error handler
chrome.runtime.onError?.addListener((error) => {
  reportError({
    type: 'runtime_error',
    message: error.message
  });
});
```

### User Feedback System

#### Feedback Collection
```javascript
// In-popup feedback
function showFeedbackForm() {
  const feedback = prompt('How can we improve Bullets?');
  if (feedback) {
    // Send to feedback service
  }
}
```

### Security Maintenance

#### Dependency Updates
```bash
# Check for outdated dependencies
npm outdated

# Update dependencies
npm update

# Audit for vulnerabilities
npm audit
npm audit fix
```

#### Security Scans
```bash
# Run security audit
npm run security:audit

# Check for known vulnerabilities
npm run security:check
```

### Regular Maintenance Tasks

#### Weekly Tasks
- [ ] Monitor error rates and user feedback
- [ ] Review and respond to Chrome Web Store reviews
- [ ] Check API usage and quotas
- [ ] Update dependencies if needed

#### Monthly Tasks
- [ ] Performance optimization review
- [ ] Security vulnerability assessment
- [ ] User engagement analysis
- [ ] Feature usage analytics review

#### Quarterly Tasks
- [ ] Major version planning
- [ ] Technology stack evaluation
- [ ] Competitor analysis
- [ ] User research and surveys

---

## Support Resources

### Documentation Links
- [Chrome Extension Development](https://developer.chrome.com/docs/extensions/)
- [Chrome Web Store Guidelines](https://developer.chrome.com/docs/webstore/)
- [Google AI Studio Documentation](https://makersuite.google.com/app/apikey)

### Community Resources
- [Chrome Extensions Google Group](https://groups.google.com/g/chrome-extensions)
- [Stack Overflow - Chrome Extensions](https://stackoverflow.com/questions/tagged/google-chrome-extension)

### Contact Information
- **GitHub Issues**: [Report bugs and feature requests](https://github.com/your-username/bullets-extension/issues)
- **Email Support**: <EMAIL>
- **Chrome Web Store**: [User reviews and support](https://chrome.google.com/webstore/detail/your-extension-id)

---

*This deployment guide should be regularly updated to reflect changes in Chrome Web Store policies and best practices.*