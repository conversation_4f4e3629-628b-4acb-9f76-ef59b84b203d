# Bullets Chrome Extension - API Reference

## Overview

This document provides comprehensive API documentation for the Bullets Chrome Extension, detailing all internal functions, data structures, and integration points.

## Table of Contents

- [Core APIs](#core-apis)
- [Background Script API](#background-script-api)
- [Content Script API](#content-script-api)
- [Popup API](#popup-api)
- [Settings API](#settings-api)
- [Data Structures](#data-structures)
- [Error Codes](#error-codes)
- [Configuration Schema](#configuration-schema)

## Core APIs

### Chrome Extensions APIs Used

| API | Purpose | Permissions Required |
|-----|---------|---------------------|
| `chrome.runtime` | Extension lifecycle, messaging | Built-in |
| `chrome.tabs` | Tab management, content injection | `"activeTab"` |
| `chrome.scripting` | Dynamic script execution | `"scripting"` |
| `chrome.contextMenus` | Right-click menu creation | `"contextMenus"` |
| `chrome.storage` | Data persistence | `"storage"` |
| `chrome.commands` | Keyboard shortcuts | `"commands"` |

## Background Script API

### Core Functions

#### `getConfig()`
Retrieves and caches extension configuration.

**Returns:** `Promise<Object>` - Configuration object
**Throws:** None - Returns default values if storage fails

**Usage:**
```javascript
const config = await getConfig();
console.log(config.MODEL); // "gemini-2.5-flash-lite"
```

#### `callOpenAIStream(text, port)`
Initiates streaming summarization request to Gemini AI.

**Parameters:**
- `text` (string): Content to summarize
- `port` (Port): Chrome runtime port for communication

**Returns:** `Promise<void>`

**Throws:** Network errors, API errors, timeout errors

#### `generateTitle(summary, port)`
Generates a concise title for the summary.

**Parameters:**
- `summary` (string): Summary text to generate title for
- `port` (Port): Chrome runtime port for communication

**Returns:** `Promise<void>`

#### `callOpenAIContextStream(text, port)`
Provides contextual background information.

**Parameters:**
- `text` (string): Original text for context analysis
- `port` (Port): Chrome runtime port for communication

**Returns:** `Promise<void>`

### Utility Functions

#### `safePostMessage(port, message)`
Safely sends messages through Chrome runtime ports.

**Parameters:**
- `port` (Port): Target port
- `message` (Object): Message to send

**Returns:** `void`

#### `bufferAndCleanContent(content)`
Processes streaming content with intelligent buffering.

**Parameters:**
- `content` (string): Raw content chunk

**Returns:** `string|null` - Processed content or null if buffering

### Message Handlers

#### Context Menu Handler
```javascript
chrome.contextMenus.onClicked.addListener((info, tab) => {
  // Handles "Bullet page" and "Bullet selection" clicks
});
```

#### Runtime Message Handler
```javascript
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // Handles configuration cache clearing and note saving
});
```

## Content Script API

### Core Functions

#### `showPopup(contentText, titleText, timeSavedText, url)`
Creates and displays the main popup interface.

**Parameters:**
- `contentText` (string): Summary content to display
- `titleText` (string): Popup title
- `timeSavedText` (string): Time saved display text
- `url` (string): Original page URL

**Returns:** `HTMLElement` - The popup element

#### `summarizeText(text)`
Orchestrates the summarization workflow.

**Parameters:**
- `text` (string): Text to summarize

**Returns:** `Promise<void>`

#### `updatePopup(newText)`
Updates popup with streaming content.

**Parameters:**
- `newText` (string): New content chunk

**Returns:** `void`

#### `hidePopup()`
Removes popup and cleans up event listeners.

**Returns:** `void`

### UI Management Functions

#### `updateTimeSavedDisplay(timeSaved)`
Updates the time saved display.

**Parameters:**
- `timeSaved` (string): Time saved text

**Returns:** `void`

#### `setupCopyButton(copyButton, summaryPanel)`
Configures copy button functionality.

**Parameters:**
- `copyButton` (HTMLElement): Copy button element
- `summaryPanel` (HTMLElement): Summary panel element

**Returns:** `void`

### Event Handlers

#### Drag and Drop
```javascript
// Mouse down handler for dragging
titleDiv.addEventListener('mousedown', (e) => {
  // Implementation details
});
```

#### Resize Handlers
```javascript
// Corner resize handler
resizeHandleCorner.addEventListener('mousedown', (e) => {
  // Implementation details
});
```

## Popup API

### Core Functions

#### `loadSavedNotes()`
Loads and displays saved notes from storage.

**Returns:** `Promise<void>`

#### `handleSearch(e)`
Processes search input and filters notes.

**Parameters:**
- `e` (Event): Input event

**Returns:** `void`

#### `handleSetApiKey()`
Manages API key input interface.

**Returns:** `void`

#### `calculateTotalTimeSaved(generatedTime)`
Calculates total time saved across all notes.

**Parameters:**
- `generatedTime` (number): Total generated time saved

**Returns:** `string` - Formatted time string

### Event Listeners

#### Note Interaction
```javascript
// Note click handler
noteItem.addEventListener('click', (e) => {
  // Open note in popup
});
```

#### Delete Handler
```javascript
deleteButton.addEventListener('click', (e) => {
  // Remove note from storage
});
```

## Settings API

### Core Functions

#### `loadSettings()`
Loads settings from Chrome storage.

**Returns:** `Promise<void>`

#### `saveSettings()`
Saves current settings to storage.

**Returns:** `Promise<void>`

#### `resetSettings()`
Resets settings to default values.

**Returns:** `Promise<void>`

#### `exportSettings()`
Exports settings as JSON file.

**Returns:** `void`

#### `importSettings(event)`
Imports settings from JSON file.

**Parameters:**
- `event` (Event): File input change event

**Returns:** `Promise<void>`

### Validation Functions

#### `validateSettings(settings)`
Validates settings object structure.

**Parameters:**
- `settings` (Object): Settings to validate

**Returns:** `boolean` - Validation result

## Data Structures

### Configuration Object
```javascript
{
  API_ENDPOINT: "https://generativelanguage.googleapis.com/v1beta/chat/completions",
  MODEL: "gemini-2.5-flash-lite",
  TIMEOUT_MS: 30000,
  TEMPERATURE: 0.7,
  API_KEY: "your-api-key",
  LANGUAGE: "french",
  CUSTOM_LANGUAGE: "",
  PROMPTS: {
    summarize: "prompt template...",
    context: "prompt template...",
    title: "prompt template..."
  }
}
```

### Note Object
```javascript
{
  title: "Generated Title",
  content: "<div>Summary HTML content</div>",
  timestamp: "2024-01-15T10:30:00.000Z",
  timeSaved: 2.5,
  url: "https://example.com/article"
}
```

### Message Formats

#### Content Script → Background Script
```javascript
// Summarization request
{
  action: "callOpenAI",
  text: "Content to summarize"
}

// Title generation request
{
  action: "generateTitle",
  summary: "Summary text"
}

// Context request
{
  action: "callOpenAIContext",
  text: "Original text"
}
```

#### Background Script → Content Script
```javascript
// Streaming data
{
  type: "data",
  content: "Streaming text chunk"
}

// Stream completion
{
  type: "done"
}

// Error notification
{
  type: "error",
  error: "Error message"
}

// Generated title
{
  type: "title",
  title: "Generated Title"
}
```

## Error Codes

### HTTP Status Codes

| Code | Meaning | Extension Handling |
|------|---------|-------------------|
| 200 | Success | Normal processing |
| 400 | Bad Request | Display "Invalid request" |
| 401 | Unauthorized | Display "Invalid API key" |
| 429 | Rate Limited | Display "Rate limit exceeded" |
| 500 | Server Error | Display "Service unavailable" |

### Extension Error Types

| Error Type | Description | User Message |
|------------|-------------|-------------|
| `API_KEY_INVALID` | Invalid API key format | "Please check your API key" |
| `NETWORK_ERROR` | Network connectivity issues | "Network error - please try again" |
| `TIMEOUT_ERROR` | Request timeout | "Request timed out - please try again" |
| `CONTEXT_INVALIDATED` | Extension context lost | "Extension was reloaded - please refresh" |
| `QUOTA_EXCEEDED` | API quota exceeded | "API quota exceeded - try again later" |

### Error Handling Patterns

```javascript
try {
  // API operation
} catch (error) {
  if (error.name === 'AbortError') {
    // Handle timeout
  } else if (error.message.includes('Extension context invalidated')) {
    // Handle extension reload
  } else {
    // Handle other errors
  }
}
```

## Configuration Schema

### Chrome Storage Schema

```javascript
// User settings
chrome.storage.local.set({
  bulletsSettings: {
    model: "gemini-2.5-flash-lite",
    language: "french",
    customLanguage: "",
    prompts: {
      summarize: "default prompt template...",
      context: "default context prompt...",
      title: "default title prompt..."
    }
  }
});

// API credentials
chrome.storage.local.set({
  customApiKey: "AIzaSy..."
});

// User data
chrome.storage.local.set({
  savedNotes: [],
  generatedTimeSaved: 0
});

// UI preferences
chrome.storage.local.set({
  popupPrefs: {
    position: { right: 20, bottom: 20 },
    size: { width: 450, height: 550 }
  }
});
```

### Default Configuration

```javascript
const DEFAULT_SETTINGS = {
  model: 'gemini-2.5-flash-lite',
  language: 'french',
  customLanguage: '',
  prompts: {
    summarize: `You are an expert reporter...`,
    context: `Provide background information...`,
    title: `Create a short title...`
  }
};
```

## Integration Examples

### Custom Integration

```javascript
// Custom summarization function
async function customSummarize(text) {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      action: 'summarizeText',
      text: text
    }, (response) => {
      if (response.success) {
        resolve(response.summary);
      } else {
        reject(response.error);
      }
    });
  });
}
```

### Event Listening

```javascript
// Listen for summarization events
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'summarizationComplete') {
    console.log('Summary completed:', message.data);
    sendResponse({ acknowledged: true });
  }
});
```

## Performance Considerations

### Optimization Techniques

1. **Configuration Caching**: Cache settings to avoid repeated storage access
2. **Stream Buffering**: Process content in chunks to avoid memory issues
3. **Lazy Loading**: Load heavy components only when needed
4. **Debounced Updates**: Limit UI updates during streaming
5. **Memory Cleanup**: Remove unused event listeners and DOM elements

### Memory Management

```javascript
// Proper cleanup pattern
function cleanup() {
  // Remove event listeners
  document.removeEventListener('mousemove', onMouseMove);
  document.removeEventListener('mouseup', onMouseUp);

  // Clear references
  popupElement = null;
  cachedConfig = null;

  // Force garbage collection hint
  if (window.gc) {
    window.gc();
  }
}
```

## Security Guidelines

### API Key Security
- Never log API keys to console
- Store keys in Chrome's encrypted storage only
- Validate key format before usage
- Implement key rotation support

### Content Security
- Sanitize all user inputs
- Validate URLs and content origins
- Use Content Security Policy headers
- Implement rate limiting for API calls

### Extension Security
- Use Manifest V3 for enhanced security
- Implement proper permission management
- Follow Chrome extension security best practices
- Regularly update dependencies

## Testing

### Unit Tests

```javascript
// Example test for bufferAndCleanContent
describe('bufferAndCleanContent', () => {
  test('should buffer incomplete lines', () => {
    const result = bufferAndCleanContent('This is a');
    expect(result).toBe('');
  });

  test('should process complete lines', () => {
    const result = bufferAndCleanContent('Complete line.\n');
    expect(result).toBe('Complete line.');
  });
});
```

### Integration Tests

```javascript
// Test full summarization workflow
async function testSummarization() {
  const text = "This is test content.";
  const summary = await summarizeText(text);
  expect(summary).toBeTruthy();
  expect(summary.length).toBeLessThan(text.length);
}
```

---

*This API reference is automatically generated from the extension codebase. For updates, modify the source files and regenerate this documentation.*